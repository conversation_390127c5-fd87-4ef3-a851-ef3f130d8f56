-- 巡更功能数据库表创建脚本
-- 创建时间: 2025-01-23

-- 1. 创建巡更配置表
DROP TABLE IF EXISTS `eh_patrol_config`;
CREATE TABLE `eh_patrol_config` (
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `location_name` varchar(100) NOT NULL COMMENT '巡更地点名称',
  `project_name` varchar(100) DEFAULT '' COMMENT '项目名称',
  `location_address` varchar(200) DEFAULT '' COMMENT '地点详细地址',
  `planned_time` varchar(5) NOT NULL COMMENT '计划巡更时间(HH:mm)',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '地点经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '地点纬度',
  `location_range` int(11) DEFAULT 100 COMMENT '允许巡更范围(米)',
  `is_active` int(11) DEFAULT 1 COMMENT '是否启用(0:禁用 1:启用)',
  `patrol_users` varchar(255) DEFAULT '' COMMENT '巡更人员姓名(逗号分隔)',
  `remark` varchar(500) DEFAULT '' COMMENT '备注说明',
  `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
  `update_time` varchar(19) DEFAULT '' COMMENT '更新时间',
  `created_by` varchar(64) DEFAULT '' COMMENT '创建人',
  `updated_by` varchar(64) DEFAULT '' COMMENT '更新人',
  PRIMARY KEY (`config_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_planned_time` (`planned_time`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡更配置表';

-- 2. 创建巡更人员配置表
DROP TABLE IF EXISTS `eh_patrol_config_user`;
CREATE TABLE `eh_patrol_config_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `user_id` varchar(32) NOT NULL COMMENT '用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '用户姓名',
  `user_phone` varchar(20) DEFAULT '' COMMENT '用户电话',
  `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_user` (`config_id`,`user_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡更人员配置表';

-- 3. 创建巡更记录表
DROP TABLE IF EXISTS `eh_patrol_record`;
CREATE TABLE `eh_patrol_record` (
  `record_id` varchar(32) NOT NULL COMMENT '记录ID',
  `config_id` varchar(32) NOT NULL COMMENT '配置ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `location_name` varchar(100) NOT NULL COMMENT '巡更地点名称',
  `location_address` varchar(200) DEFAULT '' COMMENT '地点地址',
  `planned_time` varchar(5) NOT NULL COMMENT '计划时间(HH:mm)',
  `actual_time` varchar(19) DEFAULT '' COMMENT '实际巡更时间',
  `patrol_user_id` varchar(32) NOT NULL COMMENT '巡更人员ID',
  `patrol_user_name` varchar(50) NOT NULL COMMENT '巡更人员姓名',
  `photo_file_ids` text COMMENT '照片文件ID列表(JSON格式)',
  `photo_count` int(11) DEFAULT 0 COMMENT '照片数量',
  `longitude` decimal(10,6) DEFAULT NULL COMMENT '实际巡更经度',
  `latitude` decimal(10,6) DEFAULT NULL COMMENT '实际巡更纬度',
  `location_accuracy` decimal(8,2) DEFAULT NULL COMMENT '位置精度(米)',
  `distance_from_target` decimal(8,2) DEFAULT NULL COMMENT '距离目标位置距离(米)',
  `remark` varchar(500) DEFAULT '' COMMENT '巡更备注',
  `status` int(11) DEFAULT 0 COMMENT '状态(0:待巡更 1:已完成 2:已过期)',
  `patrol_date` varchar(10) NOT NULL COMMENT '巡更日期(YYYY-MM-DD)',
  `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
  `update_time` varchar(19) DEFAULT '' COMMENT '更新时间',
  PRIMARY KEY (`record_id`),
  KEY `idx_config_id` (`config_id`),
  KEY `idx_community_id` (`community_id`),
  KEY `idx_patrol_user_id` (`patrol_user_id`),
  KEY `idx_patrol_date` (`patrol_date`),
  KEY `idx_status` (`status`),
  KEY `idx_planned_time` (`planned_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡更记录表';

-- 4. 创建巡更统计表（可选，用于快速统计查询）
DROP TABLE IF EXISTS `eh_patrol_statistics`;
CREATE TABLE `eh_patrol_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `community_id` varchar(32) NOT NULL COMMENT '社区ID',
  `stat_date` varchar(10) NOT NULL COMMENT '统计日期(YYYY-MM-DD)',
  `total_tasks` int(11) DEFAULT 0 COMMENT '总任务数',
  `completed_tasks` int(11) DEFAULT 0 COMMENT '已完成任务数',
  `overdue_tasks` int(11) DEFAULT 0 COMMENT '过期任务数',
  `completion_rate` decimal(5,2) DEFAULT 0.00 COMMENT '完成率(%)',
  `total_photos` int(11) DEFAULT 0 COMMENT '总照片数',
  `create_time` varchar(19) DEFAULT '' COMMENT '创建时间',
  `update_time` varchar(19) DEFAULT '' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_community_date` (`community_id`,`stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='巡更统计表';

-- 插入示例数据（测试用）
INSERT INTO `eh_patrol_config` VALUES 
('patrol_001', 'test_community', '小区大门', '小区主入口大门处', '09:00', 116.397428, 39.90923, 50, 1, '每日早晨巡查大门安全情况', '2025-01-23 10:00:00', '', 'admin', ''),
('patrol_002', 'test_community', '地下车库', '地下一层车库', '14:00', 116.397500, 39.909100, 100, 1, '下午巡查车库安全和设施状况', '2025-01-23 10:00:00', '', 'admin', ''),
('patrol_003', 'test_community', '小区花园', '中心花园区域', '18:00', 116.397300, 39.909300, 80, 1, '傍晚巡查花园环境和设施', '2025-01-23 10:00:00', '', 'admin', '');

INSERT INTO `eh_patrol_config_user` VALUES 
(1, 'patrol_001', 'user_001', '张三', '13800138001', '2025-01-23 10:00:00'),
(2, 'patrol_001', 'user_002', '李四', '13800138002', '2025-01-23 10:00:00'),
(3, 'patrol_002', 'user_001', '张三', '13800138001', '2025-01-23 10:00:00'),
(4, 'patrol_003', 'user_003', '王五', '13800138003', '2025-01-23 10:00:00');
